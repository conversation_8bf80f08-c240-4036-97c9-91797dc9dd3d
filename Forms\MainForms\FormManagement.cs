using System;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Helpers.FormManagement;
using ProManage.Modules.Models.FormManagement;
using ProManage.Forms.ReusableForms;

namespace ProManage.Forms.MainForms
{
    /// <summary>
    /// Form Management - Main form for managing system forms
    /// Provides CRUD operations for forms with DevExpress grid interface
    /// </summary>
    public partial class FormManagement : XtraForm
    {
        #region Properties

        /// <summary>
        /// DataTable for grid binding
        /// </summary>
        public DataTable GridDataTable { get; set; }

        /// <summary>
        /// MenuRibbon user control for consistent interface
        /// </summary>
        private MenuRibbon menuRibbon;

        #endregion

        #region Constructor

        public FormManagement()
        {
            InitializeComponent();

            // Only initialize MenuRibbon at runtime, not in designer
            if (!DesignMode)
            {
                InitializeMenuRibbon();
            }
        }

        #endregion

        #region Form Events

        private void FormManagement_Load(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== FormManagement_Load: Starting ===");

                // Initialize the grid with columns
                FormManagementHelper.InitializeGrid(this);

                // Load forms from database
                FormManagementHelper.LoadFormsToGrid(this);

                // Setup event handlers
                SetupEventHandlers();

                Debug.WriteLine("FormManagement form loaded successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading FormManagement form: {ex.Message}");
                MessageBox.Show($"Error loading Form Management: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region MenuRibbon Integration

        /// <summary>
        /// Initializes the MenuRibbon user control
        /// </summary>
        private void InitializeMenuRibbon()
        {
            try
            {
                // Skip in design mode
                if (DesignMode) return;

                menuRibbon = new MenuRibbon();
                menuRibbon.Dock = DockStyle.Top;

                // Configure ribbon for Form Management
                menuRibbon.ConfigureForFormType("FormManagement");

                // Add to form controls at the top
                this.Controls.Add(menuRibbon);
                menuRibbon.BringToFront();

                // Adjust the panel position to account for ribbon
                if (panelControl1 != null)
                {
                    panelControl1.Top = menuRibbon.Height;
                }

                Debug.WriteLine("MenuRibbon initialized for FormManagement");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing MenuRibbon: {ex.Message}");
                // Continue without ribbon if there's an error
                menuRibbon = null;
            }
        }

        /// <summary>
        /// Sets up event handlers for ribbon buttons and grid events
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                if (menuRibbon != null)
                {
                    // New button event handler
                    menuRibbon.NewClicked += MenuRibbon_NewClicked;
                    
                    // Edit button event handler
                    menuRibbon.EditClicked += MenuRibbon_EditClicked;
                    
                    // Delete button event handler
                    menuRibbon.DeleteClicked += MenuRibbon_DeleteClicked;
                }

                Debug.WriteLine("Event handlers setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up event handlers: {ex.Message}");
            }
        }

        #endregion

        #region Ribbon Event Handlers

        /// <summary>
        /// Handles New button click - creates a new form
        /// </summary>
        private void MenuRibbon_NewClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== New button clicked ===");
                FormManagementHelper.CreateNewForm(this);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in New button click: {ex.Message}");
                MessageBox.Show($"Error creating new form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Edit button click - edits selected form
        /// </summary>
        private void MenuRibbon_EditClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Edit button clicked ===");
                
                var selectedForm = FormManagementHelper.GetSelectedForm(this);
                if (selectedForm == null)
                {
                    MessageBox.Show("Please select a form to edit.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                FormManagementHelper.EditForm(this, selectedForm);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Edit button click: {ex.Message}");
                MessageBox.Show($"Error editing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Delete button click - deactivates or deletes selected form
        /// </summary>
        private void MenuRibbon_DeleteClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Delete button clicked ===");
                
                var selectedForm = FormManagementHelper.GetSelectedForm(this);
                if (selectedForm == null)
                {
                    MessageBox.Show("Please select a form to delete.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Show options for soft delete (deactivate) or hard delete
                var result = MessageBox.Show(
                    $"How would you like to remove '{selectedForm.FormName}'?\n\n" +
                    "Yes = Deactivate (recommended - preserves data)\n" +
                    "No = Permanent Delete (cannot be undone)\n" +
                    "Cancel = Do nothing",
                    "Delete Options",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        if (selectedForm.IsActive)
                        {
                            FormManagementHelper.DeactivateForm(this, selectedForm);
                        }
                        else
                        {
                            FormManagementHelper.ActivateForm(this, selectedForm);
                        }
                        break;
                    case DialogResult.No:
                        FormManagementHelper.DeleteForm(this, selectedForm);
                        break;
                    case DialogResult.Cancel:
                        // Do nothing
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Delete button click: {ex.Message}");
                MessageBox.Show($"Error deleting form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        #endregion

        #region Grid Event Handlers

        /// <summary>
        /// Handles grid selection changes to update button states
        /// </summary>
        private void GridView_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            try
            {
                FormManagementHelper.UpdateFormButtonStates(this);
                UpdateRibbonButtonStates();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in FocusedRowChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates ribbon button states based on selection
        /// </summary>
        private void UpdateRibbonButtonStates()
        {
            try
            {
                var selectedForm = FormManagementHelper.GetSelectedForm(this);
                bool hasSelection = selectedForm != null;

                if (menuRibbon != null)
                {
                    // Enable/disable buttons based on selection
                    menuRibbon.SetButtonEnabled("Edit", hasSelection);
                    menuRibbon.SetButtonEnabled("Delete", hasSelection);
                    
                    // Update delete button text based on form status
                    if (hasSelection)
                    {
                        string deleteText = selectedForm.IsActive ? "Deactivate" : "Activate";
                        menuRibbon.SetButtonText("Delete", deleteText);
                    }
                    else
                    {
                        menuRibbon.SetButtonText("Delete", "Delete");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating ribbon button states: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Refreshes the form data from database
        /// </summary>
        public void RefreshData()
        {
            FormManagementHelper.RefreshGrid(this);
        }

        /// <summary>
        /// Gets the currently selected form
        /// </summary>
        /// <returns>Selected form model or null</returns>
        public FormManagementModel GetSelectedForm()
        {
            return FormManagementHelper.GetSelectedForm(this);
        }

        #endregion
    }
}
